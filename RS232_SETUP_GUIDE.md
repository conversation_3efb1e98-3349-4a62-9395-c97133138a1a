# RS232 Balance Configuration Guide for Windows

This guide explains how to configure the Port Tanit weighing system to communicate with your balance via RS232 serial communication on Windows.

## Hardware Requirements

- Balance with RS232/RS485 interface with DB9 (9-pin D-sub) connector
- **USB-to-RS232 adapter cable** (as you have - USB on PC side, DB9 on balance side)
- Compatible drivers for your USB-to-RS232 adapter

## Balance Specifications (From Your Image)

Based on the balance specifications shown:
- **Communication Method**: RS232/RS485 interface
- **Supported Baud Rates**: 600, 1200, 2400, 4800, 9600, 19200 bps
- **Data Transmission**: Up to 1000 meters for RS485
- **Voltage**: 9600 bps DC12V transmission speed
- **Interface**: Multiple data line interfaces available

## Windows Setup Instructions

### 1. Hardware Connection

1. **Connect your USB-to-RS232 adapter cable**:
   - USB end → Connect to your Windows PC
   - DB9 (9-pin) end → Connect to your balance's RS232 port

2. **Install USB-to-RS232 drivers**:
   - Windows should automatically detect and install drivers
   - If not, download drivers from the adapter manufacturer
   - Common chipsets: FT<PERSON>, Prolific (PL2303), CH340, CP210x

3. **Identify the COM port**:
   - Open Windows Device Manager
   - Look under "Ports (COM & LPT)"
   - Note the COM port number (e.g., COM3, COM4, COM5, etc.)
   - USB adapters typically get assigned COM3 or higher numbers

### 2. Docker Configuration

#### Option A: Using Environment Variables (Recommended)

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your specific settings:
   ```env
   # Serial Port Configuration (USB-to-RS232 adapter)
   SERIAL_PORT=COM3
   SERIAL_BAUD_RATE=9600
   SERIAL_DATA_BITS=8
   SERIAL_STOP_BITS=1
   SERIAL_PARITY=none
   NETWORK_MODE=bridge
   ```

   **Note**: USB-to-RS232 adapters typically get assigned COM3 or higher. Check Device Manager for the exact COM port number.

#### Option B: Using Windows-Specific Docker Compose

Run the application with Windows-specific configuration:
```bash
docker-compose -f docker-compose.yml -f docker-compose.windows.yml up -d
```

### 3. Balance Configuration

#### Supported Baud Rates
Based on your balance specifications, configure one of these baud rates:
- 600 bps
- 1200 bps
- 2400 bps
- 4800 bps
- **9600 bps** (recommended default)
- 19200 bps

#### Communication Settings
- **Data Bits**: 8
- **Stop Bits**: 1
- **Parity**: None
- **Flow Control**: None (typically)

### 4. Application Configuration

#### Via Web Interface (Admin Panel)

1. Log in as administrator
2. Go to Settings > Weighbridge Configuration
3. Configure the following:
   - **Connection Type**: RS232
   - **Port Address**: COM1 (or your specific COM port)
   - **Baud Rate**: 9600 (or as per your balance)
   - **Data Bits**: 8
   - **Stop Bits**: 1
   - **Parity**: None
   - **Protocol Type**: ASCII
   - **Command Format**: R\r\n (or as per your balance manual)

#### Via API

Use the serial port detection API to find available ports:
```bash
curl -X GET "http://localhost:5555/api/serial-ports/com-ports" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Test a specific port:
```bash
curl -X POST "http://localhost:5555/api/serial-ports/test" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "portPath": "COM1",
    "baudRate": 9600,
    "dataBits": 8,
    "stopBits": 1,
    "parity": "none"
  }'
```

### 5. Troubleshooting

#### Common Issues

1. **USB-to-RS232 Adapter Not Recognized**
   - Check Windows Device Manager for "Unknown Device" or yellow warning icons
   - Download and install proper drivers for your adapter chipset
   - Try a different USB port
   - Some cheap adapters may not work reliably - consider a quality FTDI-based adapter

2. **Port Access Denied**
   - Ensure no other application is using the COM port
   - Check Windows Device Manager for port conflicts
   - Try running Docker as Administrator
   - Close any terminal programs (PuTTY, HyperTerminal, etc.) that might be using the port

3. **No Data Received**
   - Verify physical cable connections (USB firmly plugged in, DB9 connector secure)
   - Check baud rate matches balance configuration
   - Try different command formats (R\r\n, W\r\n, etc.)
   - Test the adapter with a terminal program first (PuTTY, Tera Term)

4. **Intermittent Connection Issues**
   - USB-to-RS232 adapters can be sensitive to power management
   - Disable USB power management in Device Manager:
     - Right-click the USB-to-Serial adapter
     - Properties → Power Management → Uncheck "Allow computer to turn off this device"

5. **Incorrect Weight Values**
   - Check data parsing patterns in the application
   - Verify unit conversion (kg, lb, tons)
   - Enable debug logging to see raw data
   - Ensure proper DB9 pin connections (some adapters may have non-standard pinouts)

#### Debug Mode

Enable debug logging by setting in your `.env` file:
```env
LOG_LEVEL=debug
```

This will show raw data received from the balance in the application logs.

### 6. Balance-Specific Configuration

#### Command Formats
Different balances may require different command formats:
- `R\r\n` - Standard read command
- `W\r\n` - Weight request
- `P\r\n` - Print/display command
- `\x05` - ENQ (Enquiry) control character

#### Response Formats
The application supports multiple response formats:
- `ST,GS,+017740 kg` - Status, Gross, Weight
- `NET: 17740 kg` - Net weight format
- `17740.5` - Simple numeric format
- `G+017740kg` - RS485 gross weight format
- `N+017740kg` - RS485 net weight format

### 7. Testing the USB-to-RS232 Connection

#### Step 1: Test with Windows Terminal Program (Recommended First)

Before using the Docker application, test your USB-to-RS232 connection:

1. **Download PuTTY** (free terminal program)
2. **Configure PuTTY**:
   - Connection Type: Serial
   - Serial Line: COM3 (or your adapter's COM port)
   - Speed: 9600 (or your balance's baud rate)
3. **Connect and test**:
   - Click "Open"
   - Type commands like `R` and press Enter
   - You should see weight data from your balance

#### Step 2: Test with Docker Application

1. Start the application:
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.windows.yml up -d
   ```

2. Check available COM ports:
   ```bash
   curl -X GET "http://localhost:5555/api/serial-ports/com-ports" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

3. Test your specific COM port:
   ```bash
   curl -X POST "http://localhost:5555/api/serial-ports/test" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -d '{
       "portPath": "COM3",
       "baudRate": 9600
     }'
   ```

4. Check the weighbridge status:
   ```bash
   curl -X GET "http://localhost:5555/api/weighbridge/status" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

5. Read weight manually:
   ```bash
   curl -X GET "http://localhost:5555/api/weighbridge/read" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

### 8. Production Deployment

For production deployment:

1. Use a dedicated COM port for the balance
2. Configure automatic startup of the Docker containers
3. Set up monitoring and alerting for connection issues
4. Regular backup of configuration settings
5. Document your specific balance model and settings

## Support

If you encounter issues:
1. Check the application logs: `docker-compose logs backend`
2. Verify Windows COM port settings in Device Manager
3. Test the balance with a terminal program (like PuTTY) first
4. Consult your balance manual for specific communication protocols
