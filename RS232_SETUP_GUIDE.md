# RS232 Balance Configuration Guide for Windows

This guide explains how to configure the Port Tanit weighing system to communicate with your balance via RS232 serial communication on Windows.

## Hardware Requirements

- Balance with RS232/RS485 interface (as shown in your image)
- RS232 to USB adapter (if your computer doesn't have a native RS232 port)
- RS232 cable (straight-through, not null-modem)

## Balance Specifications (From Your Image)

Based on the balance specifications shown:
- **Communication Method**: RS232/RS485 interface
- **Supported Baud Rates**: 600, 1200, 2400, 4800, 9600, 19200 bps
- **Data Transmission**: Up to 1000 meters for RS485
- **Voltage**: 9600 bps DC12V transmission speed
- **Interface**: Multiple data line interfaces available

## Windows Setup Instructions

### 1. Hardware Connection

1. Connect the RS232 cable from your balance to your computer's COM port
2. If using a USB-to-RS232 adapter, install the appropriate drivers
3. Note the COM port number (e.g., COM1, COM2, COM3, etc.)

### 2. Docker Configuration

#### Option A: Using Environment Variables (Recommended)

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your specific settings:
   ```env
   # Serial Port Configuration
   SERIAL_PORT=COM1
   SERIAL_BAUD_RATE=9600
   SERIAL_DATA_BITS=8
   SERIAL_STOP_BITS=1
   SERIAL_PARITY=none
   NETWORK_MODE=bridge
   ```

#### Option B: Using Windows-Specific Docker Compose

Run the application with Windows-specific configuration:
```bash
docker-compose -f docker-compose.yml -f docker-compose.windows.yml up -d
```

### 3. Balance Configuration

#### Supported Baud Rates
Based on your balance specifications, configure one of these baud rates:
- 600 bps
- 1200 bps
- 2400 bps
- 4800 bps
- **9600 bps** (recommended default)
- 19200 bps

#### Communication Settings
- **Data Bits**: 8
- **Stop Bits**: 1
- **Parity**: None
- **Flow Control**: None (typically)

### 4. Application Configuration

#### Via Web Interface (Admin Panel)

1. Log in as administrator
2. Go to Settings > Weighbridge Configuration
3. Configure the following:
   - **Connection Type**: RS232
   - **Port Address**: COM1 (or your specific COM port)
   - **Baud Rate**: 9600 (or as per your balance)
   - **Data Bits**: 8
   - **Stop Bits**: 1
   - **Parity**: None
   - **Protocol Type**: ASCII
   - **Command Format**: R\r\n (or as per your balance manual)

#### Via API

Use the serial port detection API to find available ports:
```bash
curl -X GET "http://localhost:5555/api/serial-ports/com-ports" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Test a specific port:
```bash
curl -X POST "http://localhost:5555/api/serial-ports/test" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "portPath": "COM1",
    "baudRate": 9600,
    "dataBits": 8,
    "stopBits": 1,
    "parity": "none"
  }'
```

### 5. Troubleshooting

#### Common Issues

1. **Port Access Denied**
   - Ensure no other application is using the COM port
   - Check Windows Device Manager for port conflicts
   - Try running Docker as Administrator

2. **No Data Received**
   - Verify cable connections
   - Check baud rate matches balance configuration
   - Try different command formats (R\r\n, W\r\n, etc.)

3. **Incorrect Weight Values**
   - Check data parsing patterns in the application
   - Verify unit conversion (kg, lb, tons)
   - Enable debug logging to see raw data

#### Debug Mode

Enable debug logging by setting in your `.env` file:
```env
LOG_LEVEL=debug
```

This will show raw data received from the balance in the application logs.

### 6. Balance-Specific Configuration

#### Command Formats
Different balances may require different command formats:
- `R\r\n` - Standard read command
- `W\r\n` - Weight request
- `P\r\n` - Print/display command
- `\x05` - ENQ (Enquiry) control character

#### Response Formats
The application supports multiple response formats:
- `ST,GS,+017740 kg` - Status, Gross, Weight
- `NET: 17740 kg` - Net weight format
- `17740.5` - Simple numeric format
- `G+017740kg` - RS485 gross weight format
- `N+017740kg` - RS485 net weight format

### 7. Testing the Connection

1. Start the application:
   ```bash
   docker-compose up -d
   ```

2. Check the weighbridge status:
   ```bash
   curl -X GET "http://localhost:5555/api/weighbridge/status" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

3. Read weight manually:
   ```bash
   curl -X GET "http://localhost:5555/api/weighbridge/read" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

### 8. Production Deployment

For production deployment:

1. Use a dedicated COM port for the balance
2. Configure automatic startup of the Docker containers
3. Set up monitoring and alerting for connection issues
4. Regular backup of configuration settings
5. Document your specific balance model and settings

## Support

If you encounter issues:
1. Check the application logs: `docker-compose logs backend`
2. Verify Windows COM port settings in Device Manager
3. Test the balance with a terminal program (like PuTTY) first
4. Consult your balance manual for specific communication protocols
