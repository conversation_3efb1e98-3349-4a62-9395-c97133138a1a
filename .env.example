# Port Tanit Environment Configuration
# Copy this file to .env and modify the values as needed

# Database Configuration
DB_HOST=mysql
DB_PORT=3306
DB_NAME=port_tanit
DB_USER=portuser
DB_PASSWORD=portpass123

# Application Configuration
JWT_SECRET=port-tanit-secret-key-change-in-production
PORT=5555
NODE_ENV=development

# Serial Port Configuration for Balance/Weighbridge
# Windows COM ports: COM1, COM2, COM3, etc.
# Linux/Unix ports: /dev/ttyUSB0, /dev/ttyS0, etc.
SERIAL_PORT=COM1

# Serial Communication Settings
# Based on balance specifications: 600/1200/2400/4800/9600/19200 bps
SERIAL_BAUD_RATE=9600
SERIAL_DATA_BITS=8
SERIAL_STOP_BITS=1
SERIAL_PARITY=none

# Docker Network Configuration
# Use 'host' for Windows COM port access, 'bridge' for Linux
NETWORK_MODE=bridge

# Balance/Weighbridge Configuration
BALANCE_PROTOCOL=RS232
BALANCE_MODEL=Generic
BALANCE_TIMEOUT=5000
BALANCE_RETRY_ATTEMPTS=3

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs
