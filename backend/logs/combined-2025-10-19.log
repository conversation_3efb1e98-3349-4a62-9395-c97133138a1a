{"level":"info","message":"Connexion à la base de données MySQL réussie","service":"port-tanit-backend","timestamp":"2025-10-19 13:45:23"}
{"level":"info","message":"Connexion à la base de données établie","service":"port-tanit-backend","timestamp":"2025-10-19 13:45:23"}
{"level":"info","message":"Serveur Port Tanit démarré sur le port 5555","service":"port-tanit-backend","timestamp":"2025-10-19 13:45:23"}
{"level":"info","message":"Environnement: development","service":"port-tanit-backend","timestamp":"2025-10-19 13:45:23"}
{"baudRate":9600,"level":"info","manualMode":1,"message":"Configuration de balance chargée:","port":"/dev/ttyUSB0","service":"port-tanit-backend","timestamp":"2025-10-19 13:45:23","type":"serial"}
{"level":"info","message":"Balance en mode manuel","service":"port-tanit-backend","timestamp":"2025-10-19 13:45:23"}
{"level":"info","message":"Service de balance initialisé","service":"port-tanit-backend","timestamp":"2025-10-19 13:45:23"}
{"action":"LOGIN_SUCCESS","details":{"ip":"::ffff:**********","username":"admin"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-19 13:45:49","userId":1}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-19 13:47:16"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération de l'historique: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.client_name,\n        w.origin,\n        w.destination,\n        w.notes,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      \n      ORDER BY w.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/history.js:135:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-19 13:47:16"}
{"level":"info","message":"Poids manuel défini:","service":"port-tanit-backend","timestamp":"2025-10-19 13:50:02"}
{"action":"MANUAL_WEIGHT_SET","details":{"timestamp":"2025-10-19T13:50:02.101Z","weight":21},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-19 13:50:02","userId":1}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-19 13:50:28"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération de l'historique: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.client_name,\n        w.origin,\n        w.destination,\n        w.notes,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      \n      ORDER BY w.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/history.js:135:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-19 13:50:28"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-19 13:50:37"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération de l'historique: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.client_name,\n        w.origin,\n        w.destination,\n        w.notes,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      \n      ORDER BY w.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/history.js:135:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-19 13:50:37"}
{"level":"info","message":"Signal SIGTERM reçu, arrêt du serveur...","service":"port-tanit-backend","timestamp":"2025-10-19 13:56:25"}
{"level":"info","message":"Connexion à la base de données MySQL réussie","service":"port-tanit-backend","timestamp":"2025-10-19 13:56:27"}
{"level":"info","message":"Connexion à la base de données établie","service":"port-tanit-backend","timestamp":"2025-10-19 13:56:27"}
{"level":"info","message":"Serveur Port Tanit démarré sur le port 5555","service":"port-tanit-backend","timestamp":"2025-10-19 13:56:27"}
{"level":"info","message":"Environnement: development","service":"port-tanit-backend","timestamp":"2025-10-19 13:56:27"}
{"baudRate":9600,"level":"info","manualMode":1,"message":"Configuration de balance chargée:","port":"/dev/ttyUSB0","service":"port-tanit-backend","timestamp":"2025-10-19 13:56:27","type":"serial"}
{"level":"info","message":"Balance en mode manuel","service":"port-tanit-backend","timestamp":"2025-10-19 13:56:27"}
{"level":"info","message":"Service de balance initialisé","service":"port-tanit-backend","timestamp":"2025-10-19 13:56:27"}
{"level":"info","message":"Signal SIGTERM reçu, arrêt du serveur...","service":"port-tanit-backend","timestamp":"2025-10-19 13:57:41"}
{"level":"info","message":"Connexion à la base de données MySQL réussie","service":"port-tanit-backend","timestamp":"2025-10-19 13:57:43"}
{"level":"info","message":"Connexion à la base de données établie","service":"port-tanit-backend","timestamp":"2025-10-19 13:57:43"}
{"level":"info","message":"Serveur Port Tanit démarré sur le port 5555","service":"port-tanit-backend","timestamp":"2025-10-19 13:57:43"}
{"level":"info","message":"Environnement: development","service":"port-tanit-backend","timestamp":"2025-10-19 13:57:43"}
{"baudRate":9600,"level":"info","manualMode":1,"message":"Configuration de balance chargée:","port":"/dev/ttyUSB0","service":"port-tanit-backend","timestamp":"2025-10-19 13:57:43","type":"serial"}
{"level":"info","message":"Balance en mode manuel","service":"port-tanit-backend","timestamp":"2025-10-19 13:57:43"}
{"level":"info","message":"Service de balance initialisé","service":"port-tanit-backend","timestamp":"2025-10-19 13:57:43"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT\n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        ...","timestamp":"2025-10-19 13:58:23"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération de l'historique: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT\n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.client_name,\n        w.origin,\n        w.destination,\n        w.notes,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      LEFT JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      \n      ORDER BY w.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/history.js:137:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-19 13:58:23"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT\n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        ...","timestamp":"2025-10-19 13:58:28"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération de l'historique: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT\n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.client_name,\n        w.origin,\n        w.destination,\n        w.notes,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      LEFT JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      \n      ORDER BY w.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/history.js:137:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-19 13:58:28"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT\n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        ...","timestamp":"2025-10-19 13:58:29"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération de l'historique: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT\n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.client_name,\n        w.origin,\n        w.destination,\n        w.notes,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      LEFT JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      \n      ORDER BY w.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/history.js:137:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-19 13:58:29"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT\n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        ...","timestamp":"2025-10-19 13:58:34"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération de l'historique: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT\n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.client_name,\n        w.origin,\n        w.destination,\n        w.notes,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      LEFT JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      \n      ORDER BY w.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/history.js:137:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-19 13:58:34"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT\n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        ...","timestamp":"2025-10-19 13:58:46"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération de l'historique: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT\n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.client_name,\n        w.origin,\n        w.destination,\n        w.notes,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      LEFT JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      \n      ORDER BY w.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/history.js:137:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-19 13:58:46"}
{"level":"info","message":"Signal SIGTERM reçu, arrêt du serveur...","service":"port-tanit-backend","timestamp":"2025-10-19 14:00:04"}
{"level":"info","message":"Connexion à la base de données MySQL réussie","service":"port-tanit-backend","timestamp":"2025-10-19 14:00:07"}
{"level":"info","message":"Connexion à la base de données établie","service":"port-tanit-backend","timestamp":"2025-10-19 14:00:07"}
{"level":"info","message":"Serveur Port Tanit démarré sur le port 5555","service":"port-tanit-backend","timestamp":"2025-10-19 14:00:07"}
{"level":"info","message":"Environnement: development","service":"port-tanit-backend","timestamp":"2025-10-19 14:00:07"}
{"baudRate":9600,"level":"info","manualMode":1,"message":"Configuration de balance chargée:","port":"/dev/ttyUSB0","service":"port-tanit-backend","timestamp":"2025-10-19 14:00:07","type":"serial"}
{"level":"info","message":"Balance en mode manuel","service":"port-tanit-backend","timestamp":"2025-10-19 14:00:07"}
{"level":"info","message":"Service de balance initialisé","service":"port-tanit-backend","timestamp":"2025-10-19 14:00:07"}
{"action":"LOGOUT","details":{"ip":"::ffff:**********","username":"admin"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-19 14:05:16","userId":1}
{"action":"LOGIN_SUCCESS","details":{"ip":"::ffff:**********","username":"admin"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-19 14:06:57","userId":1}
{"level":"info","message":"Poids manuel défini:","service":"port-tanit-backend","timestamp":"2025-10-19 14:07:16"}
{"action":"MANUAL_WEIGHT_SET","details":{"timestamp":"2025-10-19T14:07:16.815Z","weight":2000},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-19 14:07:16","userId":1}
{"data":{"entryWeight":2000,"matricule":"1200","operatorId":1,"productId":1,"ticketNumber":"20251019-893","weighingId":2},"level":"info","message":"WEIGHING_OPERATION","operation":"ENTRY_CREATED","service":"port-tanit-backend","timestamp":"2025-10-19 14:07:54"}
{"level":"info","message":"Poids manuel défini:","service":"port-tanit-backend","timestamp":"2025-10-19 14:08:28"}
{"action":"MANUAL_WEIGHT_SET","details":{"timestamp":"2025-10-19T14:08:28.988Z","weight":2500},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-19 14:08:28","userId":1}
{"data":{"amount":600000,"entryWeight":"2000.00","exitWeight":2500,"matricule":"1200","netWeight":500,"operatorId":1,"ticketNumber":"20251019-893","weighingId":"2"},"level":"info","message":"WEIGHING_OPERATION","operation":"EXIT_COMPLETED","service":"port-tanit-backend","timestamp":"2025-10-19 14:08:52"}
{"level":"info","message":"Signal SIGTERM reçu, arrêt du serveur...","service":"port-tanit-backend","timestamp":"2025-10-19 16:19:02"}
