{"level":"info","message":"Connexion à la base de données MySQL réussie","service":"port-tanit-backend","timestamp":"2025-10-12 12:05:18"}
{"level":"info","message":"Connexion à la base de données établie","service":"port-tanit-backend","timestamp":"2025-10-12 12:05:18"}
{"level":"info","message":"Serveur Port Tanit démarré sur le port 5555","service":"port-tanit-backend","timestamp":"2025-10-12 12:05:18"}
{"level":"info","message":"Environnement: development","service":"port-tanit-backend","timestamp":"2025-10-12 12:05:18"}
{"baudRate":9600,"level":"info","manualMode":1,"message":"Configuration de balance chargée:","port":"/dev/ttyUSB0","service":"port-tanit-backend","timestamp":"2025-10-12 12:05:18","type":"serial"}
{"level":"info","message":"Balance en mode manuel","service":"port-tanit-backend","timestamp":"2025-10-12 12:05:18"}
{"level":"info","message":"Service de balance initialisé","service":"port-tanit-backend","timestamp":"2025-10-12 12:05:18"}
{"level":"info","message":"Signal SIGTERM reçu, arrêt du serveur...","service":"port-tanit-backend","timestamp":"2025-10-12 12:09:03"}
{"level":"info","message":"Connexion à la base de données MySQL réussie","service":"port-tanit-backend","timestamp":"2025-10-12 12:09:09"}
{"level":"info","message":"Connexion à la base de données établie","service":"port-tanit-backend","timestamp":"2025-10-12 12:09:09"}
{"level":"info","message":"Serveur Port Tanit démarré sur le port 5555","service":"port-tanit-backend","timestamp":"2025-10-12 12:09:09"}
{"level":"info","message":"Environnement: development","service":"port-tanit-backend","timestamp":"2025-10-12 12:09:09"}
{"baudRate":9600,"level":"info","manualMode":1,"message":"Configuration de balance chargée:","port":"/dev/ttyUSB0","service":"port-tanit-backend","timestamp":"2025-10-12 12:09:09","type":"serial"}
{"level":"info","message":"Balance en mode manuel","service":"port-tanit-backend","timestamp":"2025-10-12 12:09:09"}
{"level":"info","message":"Service de balance initialisé","service":"port-tanit-backend","timestamp":"2025-10-12 12:09:09"}
{"action":"LOGIN_FAILED","details":{"ip":"::ffff:**********","reason":"User not found","username":"test"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-12 12:15:34","userId":null}
{"action":"LOGIN_FAILED","details":{"ip":"::ffff:**********","reason":"User not found","username":"<EMAIL>"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-12 12:15:48","userId":null}
{"action":"LOGIN_SUCCESS","details":{"ip":"::ffff:**********","username":"admin"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-12 12:16:21","userId":1}
{"action":"LOGIN_SUCCESS","details":{"ip":"::ffff:**********","username":"admin"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-12 12:16:27","userId":1}
{"action":"LOGIN_SUCCESS","details":{"ip":"::ffff:**********","username":"admin"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-12 12:17:31","userId":1}
{"level":"info","message":"Signal SIGTERM reçu, arrêt du serveur...","service":"port-tanit-backend","timestamp":"2025-10-12 12:21:36"}
{"level":"info","message":"Connexion à la base de données MySQL réussie","service":"port-tanit-backend","timestamp":"2025-10-12 12:54:09"}
{"level":"info","message":"Connexion à la base de données établie","service":"port-tanit-backend","timestamp":"2025-10-12 12:54:09"}
{"level":"info","message":"Serveur Port Tanit démarré sur le port 5555","service":"port-tanit-backend","timestamp":"2025-10-12 12:54:09"}
{"level":"info","message":"Environnement: development","service":"port-tanit-backend","timestamp":"2025-10-12 12:54:09"}
{"baudRate":9600,"level":"info","manualMode":1,"message":"Configuration de balance chargée:","port":"/dev/ttyUSB0","service":"port-tanit-backend","timestamp":"2025-10-12 12:54:09","type":"serial"}
{"level":"info","message":"Balance en mode manuel","service":"port-tanit-backend","timestamp":"2025-10-12 12:54:09"}
{"level":"info","message":"Service de balance initialisé","service":"port-tanit-backend","timestamp":"2025-10-12 12:54:09"}
{"action":"LOGIN_SUCCESS","details":{"ip":"::ffff:**********","username":"admin"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-12 12:54:28","userId":1}
{"level":"info","message":"Signal SIGTERM reçu, arrêt du serveur...","service":"port-tanit-backend","timestamp":"2025-10-12 13:03:13"}
{"level":"info","message":"Connexion à la base de données MySQL réussie","service":"port-tanit-backend","timestamp":"2025-10-12 13:03:16"}
{"level":"info","message":"Connexion à la base de données établie","service":"port-tanit-backend","timestamp":"2025-10-12 13:03:16"}
{"level":"info","message":"Serveur Port Tanit démarré sur le port 5555","service":"port-tanit-backend","timestamp":"2025-10-12 13:03:16"}
{"level":"info","message":"Environnement: development","service":"port-tanit-backend","timestamp":"2025-10-12 13:03:16"}
{"baudRate":9600,"level":"info","manualMode":1,"message":"Configuration de balance chargée:","port":"/dev/ttyUSB0","service":"port-tanit-backend","timestamp":"2025-10-12 13:03:16","type":"serial"}
{"level":"info","message":"Balance en mode manuel","service":"port-tanit-backend","timestamp":"2025-10-12 13:03:16"}
{"level":"info","message":"Service de balance initialisé","service":"port-tanit-backend","timestamp":"2025-10-12 13:03:16"}
{"action":"LOGIN_SUCCESS","details":{"ip":"::ffff:**********","username":"admin"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-12 21:50:29","userId":1}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-12 22:00:58"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération des transactions récentes: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        w.client_name,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      WHERE w.status != 'cancelled'\n      ORDER BY w.created_at DESC\n      LIMIT ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/dashboard.js:105:38\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at verifyToken (/app/middleware/auth.js:69:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-12 22:00:58"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-12 22:01:01"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération des transactions récentes: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        w.client_name,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      WHERE w.status != 'cancelled'\n      ORDER BY w.created_at DESC\n      LIMIT ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/dashboard.js:105:38\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at verifyToken (/app/middleware/auth.js:69:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-12 22:01:01"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-12 22:01:55"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération des transactions récentes: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        w.client_name,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      WHERE w.status != 'cancelled'\n      ORDER BY w.created_at DESC\n      LIMIT ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/dashboard.js:105:38\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at verifyToken (/app/middleware/auth.js:69:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-12 22:01:55"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-12 22:06:55"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération des transactions récentes: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        w.client_name,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      WHERE w.status != 'cancelled'\n      ORDER BY w.created_at DESC\n      LIMIT ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/dashboard.js:105:38\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at verifyToken (/app/middleware/auth.js:69:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-12 22:06:55"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-12 22:11:55"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération des transactions récentes: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        w.client_name,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      WHERE w.status != 'cancelled'\n      ORDER BY w.created_at DESC\n      LIMIT ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/dashboard.js:105:38\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at verifyToken (/app/middleware/auth.js:69:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-12 22:11:55"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-12 22:14:02"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération des transactions récentes: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        w.client_name,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      WHERE w.status != 'cancelled'\n      ORDER BY w.created_at DESC\n      LIMIT ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/dashboard.js:105:38\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at verifyToken (/app/middleware/auth.js:69:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-12 22:14:02"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-12 22:14:32"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération des transactions récentes: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        w.client_name,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      WHERE w.status != 'cancelled'\n      ORDER BY w.created_at DESC\n      LIMIT ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/dashboard.js:105:38\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at verifyToken (/app/middleware/auth.js:69:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-12 22:14:32"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-12 22:16:00"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération des transactions récentes: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        w.client_name,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      WHERE w.status != 'cancelled'\n      ORDER BY w.created_at DESC\n      LIMIT ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/dashboard.js:105:38\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at verifyToken (/app/middleware/auth.js:69:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-12 22:16:00"}
{"level":"info","message":"Signal SIGTERM reçu, arrêt du serveur...","service":"port-tanit-backend","timestamp":"2025-10-12 22:16:37"}
{"level":"info","message":"Connexion à la base de données MySQL réussie","service":"port-tanit-backend","timestamp":"2025-10-12 22:16:39"}
{"level":"info","message":"Connexion à la base de données établie","service":"port-tanit-backend","timestamp":"2025-10-12 22:16:39"}
{"level":"info","message":"Serveur Port Tanit démarré sur le port 5555","service":"port-tanit-backend","timestamp":"2025-10-12 22:16:39"}
{"level":"info","message":"Environnement: development","service":"port-tanit-backend","timestamp":"2025-10-12 22:16:39"}
{"baudRate":9600,"level":"info","manualMode":1,"message":"Configuration de balance chargée:","port":"/dev/ttyUSB0","service":"port-tanit-backend","timestamp":"2025-10-12 22:16:39","type":"serial"}
{"level":"info","message":"Balance en mode manuel","service":"port-tanit-backend","timestamp":"2025-10-12 22:16:39"}
{"level":"info","message":"Service de balance initialisé","service":"port-tanit-backend","timestamp":"2025-10-12 22:16:39"}
{"level":"info","message":"Poids manuel défini:","service":"port-tanit-backend","timestamp":"2025-10-12 22:17:09"}
{"action":"MANUAL_WEIGHT_SET","details":{"timestamp":"2025-10-12T22:17:09.410Z","weight":200},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-12 22:17:09","userId":1}
{"data":{"entryWeight":200,"matricule":"11111","operatorId":1,"productId":1,"ticketNumber":"20251012-217","weighingId":1},"level":"info","message":"WEIGHING_OPERATION","operation":"ENTRY_CREATED","service":"port-tanit-backend","timestamp":"2025-10-12 22:17:39"}
{"action":"LOGIN_SUCCESS","details":{"ip":"::ffff:**********","username":"admin"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-12 22:18:40","userId":1}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-12 22:21:31"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération de l'historique: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.client_name,\n        w.origin,\n        w.destination,\n        w.notes,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      \n      ORDER BY w.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/history.js:135:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-12 22:21:31"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-12 22:22:24"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération de l'historique: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.client_name,\n        w.origin,\n        w.destination,\n        w.notes,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      \n      ORDER BY w.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/history.js:135:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-12 22:22:24"}
{"action":"LOGOUT","details":{"ip":"::ffff:**********","username":"admin"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-12 22:27:19","userId":1}
{"action":"LOGIN_SUCCESS","details":{"ip":"::ffff:**********","username":"admin"},"level":"info","message":"USER_ACTION","service":"port-tanit-backend","timestamp":"2025-10-12 22:27:40","userId":1}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Erreur lors de l'exécution de la requête:","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n       ...","timestamp":"2025-10-12 22:28:11"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Erreur lors de la récupération de l'historique: Incorrect arguments to mysqld_stmt_execute","service":"port-tanit-backend","sql":"\n      SELECT \n        w.id,\n        w.ticket_number,\n        w.matricule,\n        w.status,\n        w.entry_weight,\n        w.exit_weight,\n        w.net_weight,\n        w.amount_to_pay,\n        w.payment_status,\n        w.client_name,\n        w.origin,\n        w.destination,\n        w.notes,\n        w.created_at,\n        w.entry_datetime,\n        w.exit_datetime,\n        p.name as product_name,\n        p.code as product_code,\n        u1.full_name as entry_operator_name,\n        u2.full_name as exit_operator_name\n      FROM weighings w\n      JOIN products p ON w.product_id = p.id\n      JOIN users u1 ON w.entry_operator_id = u1.id\n      LEFT JOIN users u2 ON w.exit_operator_id = u2.id\n      \n      ORDER BY w.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (/app/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at query (/app/config/database.js:40:31)\n    at /app/routes/history.js:135:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-10-12 22:28:11"}
