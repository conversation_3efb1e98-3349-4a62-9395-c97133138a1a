{"name": "port-tanit-backend", "version": "1.0.0", "description": "Backend API pour le système de gestion de pesage Port Tanit", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "detect-ports": "node ../scripts/detect-com-ports.js", "test-port": "node ../scripts/detect-com-ports.js"}, "keywords": ["weighbridge", "port", "tanit", "mauritanie"], "author": "Port Tanit Development Team", "license": "PROPRIETARY", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "serialport": "^12.0.0", "pdfkit": "^0.13.0", "qrcode": "^1.5.3", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1", "node-cron": "^3.0.2", "csv-writer": "^1.6.0", "xlsx": "^0.18.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}