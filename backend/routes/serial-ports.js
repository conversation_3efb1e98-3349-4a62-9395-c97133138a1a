const express = require('express');
const { body, validationResult } = require('express-validator');
const { verifyToken, requireAdmin } = require('../middleware/auth');
const serialPortDetection = require('../services/SerialPortDetection');
const logger = require('../utils/logger');

const router = express.Router();

// Get available serial ports
router.get('/list', [verifyToken, requireAdmin], async (req, res) => {
  try {
    const ports = await serialPortDetection.getAvailablePorts();
    
    res.json({
      success: true,
      data: {
        ports,
        platform: process.platform,
        totalPorts: ports.length,
        recommendedPorts: ports.filter(p => p.isRecommended).length
      }
    });

  } catch (error) {
    logger.error('Error listing serial ports:', error);
    res.status(500).json({
      error: 'Failed to list serial ports',
      details: error.message
    });
  }
});

// Get Windows COM ports specifically
router.get('/com-ports', [verifyToken, requireAdmin], async (req, res) => {
  try {
    const comPorts = await serialPortDetection.getWindowsComPorts();
    
    res.json({
      success: true,
      data: {
        comPorts,
        platform: process.platform,
        isWindows: process.platform === 'win32'
      }
    });

  } catch (error) {
    logger.error('Error getting COM ports:', error);
    res.status(500).json({
      error: 'Failed to get COM ports',
      details: error.message
    });
  }
});

// Test connection to a specific port
router.post('/test', [
  verifyToken,
  requireAdmin,
  body('portPath')
    .notEmpty()
    .withMessage('Port path is required'),
  body('baudRate')
    .optional()
    .isInt({ min: 300, max: 115200 })
    .withMessage('Invalid baud rate'),
  body('dataBits')
    .optional()
    .isInt({ min: 7, max: 8 })
    .withMessage('Invalid data bits'),
  body('stopBits')
    .optional()
    .isInt({ min: 1, max: 2 })
    .withMessage('Invalid stop bits'),
  body('parity')
    .optional()
    .isIn(['none', 'odd', 'even'])
    .withMessage('Invalid parity')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Invalid parameters',
        details: errors.array()
      });
    }

    const { portPath, baudRate, dataBits, stopBits, parity } = req.body;
    
    const testOptions = {
      baudRate: baudRate || 9600,
      dataBits: dataBits || 8,
      stopBits: stopBits || 1,
      parity: parity || 'none'
    };

    const testResult = await serialPortDetection.testPortConnection(portPath, testOptions);
    
    logger.logUserAction(req.user.id, 'SERIAL_PORT_TEST', {
      portPath,
      options: testOptions,
      result: testResult.success
    });

    res.json({
      success: true,
      data: testResult
    });

  } catch (error) {
    logger.error('Error testing serial port:', error);
    res.status(500).json({
      error: 'Failed to test serial port',
      details: error.message
    });
  }
});

// Auto-detect weighbridge port
router.post('/auto-detect', [verifyToken, requireAdmin], async (req, res) => {
  try {
    const { testBaudRates } = req.body;
    
    const detectionResult = await serialPortDetection.autoDetectWeighbridgePort(testBaudRates);
    
    logger.logUserAction(req.user.id, 'WEIGHBRIDGE_AUTO_DETECT', {
      result: detectionResult ? 'found' : 'not_found',
      port: detectionResult?.portPath
    });

    if (detectionResult) {
      res.json({
        success: true,
        data: detectionResult,
        message: 'Weighbridge port detected successfully'
      });
    } else {
      res.json({
        success: false,
        message: 'No suitable weighbridge port found',
        data: null
      });
    }

  } catch (error) {
    logger.error('Error during auto-detection:', error);
    res.status(500).json({
      error: 'Auto-detection failed',
      details: error.message
    });
  }
});

// Get recommended configuration
router.get('/recommended-config', [verifyToken, requireAdmin], async (req, res) => {
  try {
    const config = serialPortDetection.getRecommendedConfig();
    
    res.json({
      success: true,
      data: config
    });

  } catch (error) {
    logger.error('Error getting recommended config:', error);
    res.status(500).json({
      error: 'Failed to get recommended configuration',
      details: error.message
    });
  }
});

// Get serial port detection status
router.get('/status', [verifyToken, requireAdmin], async (req, res) => {
  try {
    const status = serialPortDetection.getStatus();
    
    res.json({
      success: true,
      data: status
    });

  } catch (error) {
    logger.error('Error getting serial port status:', error);
    res.status(500).json({
      error: 'Failed to get status',
      details: error.message
    });
  }
});

module.exports = router;
