const { SerialPort } = require('serialport');
const logger = require('../utils/logger');
const os = require('os');

class SerialPortDetection {
  constructor() {
    this.platform = os.platform();
    this.availablePorts = [];
    this.lastScanTime = null;
    this.scanInterval = 30000; // 30 seconds
  }

  /**
   * Get list of available serial ports
   * @returns {Promise<Array>} Array of port information
   */
  async getAvailablePorts() {
    try {
      const ports = await SerialPort.list();
      this.availablePorts = ports.map(port => ({
        path: port.path,
        manufacturer: port.manufacturer || 'Unknown',
        serialNumber: port.serialNumber || 'Unknown',
        pnpId: port.pnpId || 'Unknown',
        locationId: port.locationId || 'Unknown',
        vendorId: port.vendorId || 'Unknown',
        productId: port.productId || 'Unknown',
        platform: this.platform,
        isRecommended: this.isRecommendedPort(port)
      }));

      this.lastScanTime = new Date();
      logger.info(`Found ${this.availablePorts.length} serial ports`, {
        ports: this.availablePorts.map(p => ({ path: p.path, manufacturer: p.manufacturer }))
      });

      return this.availablePorts;
    } catch (error) {
      logger.error('Error scanning for serial ports:', error);
      throw error;
    }
  }

  /**
   * Check if a port is recommended for weighbridge use
   * @param {Object} port - Port information
   * @returns {boolean} True if recommended
   */
  isRecommendedPort(port) {
    const recommendedManufacturers = [
      'FTDI', 'Prolific', 'Silicon Labs', 'CH340', 'CP210x'
    ];
    
    const recommendedPaths = this.platform === 'win32' 
      ? ['COM1', 'COM2', 'COM3', 'COM4']
      : ['/dev/ttyUSB0', '/dev/ttyUSB1', '/dev/ttyS0', '/dev/ttyS1'];

    return recommendedManufacturers.some(mfg => 
      port.manufacturer && port.manufacturer.toLowerCase().includes(mfg.toLowerCase())
    ) || recommendedPaths.includes(port.path);
  }

  /**
   * Test connection to a specific port
   * @param {string} portPath - Path to the serial port
   * @param {Object} options - Connection options
   * @returns {Promise<Object>} Test result
   */
  async testPortConnection(portPath, options = {}) {
    const defaultOptions = {
      baudRate: 9600,
      dataBits: 8,
      stopBits: 1,
      parity: 'none',
      timeout: 5000
    };

    const portOptions = { ...defaultOptions, ...options, path: portPath, autoOpen: false };

    return new Promise((resolve) => {
      const testPort = new SerialPort(portOptions);
      const timeoutId = setTimeout(() => {
        testPort.close();
        resolve({
          success: false,
          error: 'Connection timeout',
          portPath,
          options: portOptions
        });
      }, portOptions.timeout);

      testPort.on('error', (error) => {
        clearTimeout(timeoutId);
        resolve({
          success: false,
          error: error.message,
          portPath,
          options: portOptions
        });
      });

      testPort.on('open', () => {
        clearTimeout(timeoutId);
        testPort.close();
        resolve({
          success: true,
          message: 'Port opened successfully',
          portPath,
          options: portOptions
        });
      });

      testPort.open();
    });
  }

  /**
   * Get Windows-specific COM port information
   * @returns {Promise<Array>} Array of COM port details
   */
  async getWindowsComPorts() {
    if (this.platform !== 'win32') {
      return [];
    }

    try {
      const ports = await this.getAvailablePorts();
      return ports.filter(port => port.path.startsWith('COM'));
    } catch (error) {
      logger.error('Error getting Windows COM ports:', error);
      return [];
    }
  }

  /**
   * Get recommended port configuration for weighbridge
   * @returns {Object} Recommended configuration
   */
  getRecommendedConfig() {
    const baseConfig = {
      baudRate: 9600,
      dataBits: 8,
      stopBits: 1,
      parity: 'none',
      flowControl: 'none',
      timeout: 5000,
      retryAttempts: 3
    };

    if (this.platform === 'win32') {
      return {
        ...baseConfig,
        portPath: 'COM1',
        platform: 'windows',
        supportedBaudRates: [600, 1200, 2400, 4800, 9600, 19200],
        notes: 'For Windows RS232 communication. Ensure COM port is not in use by other applications.'
      };
    } else {
      return {
        ...baseConfig,
        portPath: '/dev/ttyUSB0',
        platform: 'linux',
        supportedBaudRates: [600, 1200, 2400, 4800, 9600, 19200],
        notes: 'For Linux/Unix RS232 communication. Ensure user has permission to access serial ports.'
      };
    }
  }

  /**
   * Auto-detect best port for weighbridge
   * @param {Array} testBaudRates - Baud rates to test
   * @returns {Promise<Object>} Best port configuration or null
   */
  async autoDetectWeighbridgePort(testBaudRates = [9600, 19200, 4800, 2400]) {
    try {
      const ports = await this.getAvailablePorts();
      const recommendedPorts = ports.filter(port => port.isRecommended);
      
      logger.info(`Testing ${recommendedPorts.length} recommended ports for weighbridge connection`);

      for (const port of recommendedPorts) {
        for (const baudRate of testBaudRates) {
          const testResult = await this.testPortConnection(port.path, { baudRate });
          if (testResult.success) {
            logger.info(`Found working port: ${port.path} at ${baudRate} baud`);
            return {
              portPath: port.path,
              baudRate,
              manufacturer: port.manufacturer,
              recommended: true,
              testResult
            };
          }
        }
      }

      logger.warn('No working weighbridge port found during auto-detection');
      return null;
    } catch (error) {
      logger.error('Error during auto-detection:', error);
      return null;
    }
  }

  /**
   * Get port status information
   * @returns {Object} Status information
   */
  getStatus() {
    return {
      platform: this.platform,
      lastScanTime: this.lastScanTime,
      availablePortsCount: this.availablePorts.length,
      recommendedPorts: this.availablePorts.filter(p => p.isRecommended).length,
      scanInterval: this.scanInterval
    };
  }
}

module.exports = new SerialPortDetection();
