const { SerialPort } = require('serialport');
const { ReadlineParser } = require('@serialport/parser-readline');
const EventEmitter = require('events');
const { queryOne, update } = require('../config/database');
const logger = require('../utils/logger');

class WeighbridgeService extends EventEmitter {
  constructor() {
    super();
    this.port = null;
    this.parser = null;
    this.config = null;
    this.isConnected = false;
    this.currentWeight = 0;
    this.lastStableWeight = 0;
    this.stableReadings = 0;
    this.requiredStableReadings = 3;
    this.readingInterval = null;
    this.manualMode = false;
  }

  // Initialize weighbridge service
  async initialize() {
    try {
      await this.loadConfiguration();
      
      if (this.config.manual_mode) {
        this.manualMode = true;
        logger.info('Balance en mode manuel');
        return true;
      }

      await this.connect();
      return true;
    } catch (error) {
      logger.error('Erreur lors de l\'initialisation de la balance:', error);
      return false;
    }
  }

  // Load configuration from database
  async loadConfiguration() {
    try {
      this.config = await queryOne(
        'SELECT * FROM weighbridge_config WHERE active = true ORDER BY id DESC LIMIT 1'
      );

      if (!this.config) {
        // Create default configuration based on platform
        const isWindows = process.platform === 'win32';
        this.config = {
          connection_type: 'rs232',
          port_address: isWindows ? 'COM1' : '/dev/ttyUSB0',
          baud_rate: 9600,
          data_bits: 8,
          stop_bits: 1,
          parity: 'none',
          flow_control: 'none',
          protocol_type: 'ascii',
          command_format: 'R\\r\\n',
          response_format: 'weight_kg',
          timeout_ms: 5000,
          retry_attempts: 3,
          stable_readings_required: 3,
          weight_tolerance: 1.00,
          manual_mode: true,
          auto_read_interval: 2000,
          platform: isWindows ? 'windows' : 'linux',
          active: true
        };
        logger.warn('Aucune configuration de balance trouvée, utilisation des valeurs par défaut');
      }

      // Set instance properties from config
      this.requiredStableReadings = this.config.stable_readings_required || 3;
      this.weightTolerance = this.config.weight_tolerance || 1.00;
      this.timeout = this.config.timeout_ms || 5000;
      this.retryAttempts = this.config.retry_attempts || 3;
      this.autoReadInterval = this.config.auto_read_interval || 2000;

      logger.info('Configuration de balance chargée:', {
        type: this.config.connection_type,
        port: this.config.port_address,
        baudRate: this.config.baud_rate,
        platform: this.config.platform,
        protocol: this.config.protocol_type,
        manualMode: this.config.manual_mode
      });

    } catch (error) {
      logger.error('Erreur lors du chargement de la configuration:', error);
      throw error;
    }
  }

  // Connect to weighbridge
  async connect() {
    try {
      if (this.isConnected) {
        await this.disconnect();
      }

      if (this.config.connection_type === 'serial') {
        await this.connectSerial();
      } else {
        throw new Error(`Type de connexion non supporté: ${this.config.connection_type}`);
      }

      this.isConnected = true;
      this.emit('connected');
      logger.info('Connexion à la balance établie');

    } catch (error) {
      logger.error('Erreur lors de la connexion à la balance:', error);
      this.isConnected = false;
      this.emit('error', error);
      throw error;
    }
  }

  // Connect via serial port
  async connectSerial() {
    return new Promise((resolve, reject) => {
      const portOptions = {
        path: this.config.port_address,
        baudRate: this.config.baud_rate,
        dataBits: this.config.data_bits,
        stopBits: this.config.stop_bits,
        parity: this.config.parity === 'none' ? false : this.config.parity,
        autoOpen: false
      };

      // Add flow control if specified
      if (this.config.flow_control && this.config.flow_control !== 'none') {
        switch (this.config.flow_control) {
          case 'rts_cts':
            portOptions.rtscts = true;
            break;
          case 'xon_xoff':
            portOptions.xon = true;
            portOptions.xoff = true;
            break;
          case 'dtr_dsr':
            // DTR/DSR flow control (less common)
            break;
        }
      }

      logger.info('Tentative de connexion au port série:', portOptions);
      this.port = new SerialPort(portOptions);

      // Set timeout for connection attempt
      const connectionTimeout = setTimeout(() => {
        this.port.close();
        reject(new Error(`Timeout de connexion au port ${this.config.port_address}`));
      }, this.timeout);

      this.port.on('error', (error) => {
        clearTimeout(connectionTimeout);
        logger.error('Erreur du port série:', error);
        reject(error);
      });

      this.port.on('open', () => {
        clearTimeout(connectionTimeout);
        logger.info(`Port série ouvert: ${this.config.port_address} à ${this.config.baud_rate} baud`);
        this.setupParser();
        resolve();
      });

      this.port.on('close', () => {
        logger.info('Port série fermé');
        this.isConnected = false;
        this.emit('disconnected');
      });

      this.port.open();
    });
  }

  // Setup data parser
  setupParser() {
    // Choose delimiter based on protocol type
    const delimiter = this.getDelimiterForProtocol();
    this.parser = this.port.pipe(new ReadlineParser({ delimiter }));

    this.parser.on('data', (data) => {
      try {
        const weight = this.parseWeightData(data);
        if (weight !== null) {
          this.processWeightReading(weight);
        }
      } catch (error) {
        logger.error('Erreur lors du parsing des données:', error);
      }
    });

    // Start reading interval based on configuration
    this.startReadingInterval();
  }

  // Get delimiter based on protocol configuration
  getDelimiterForProtocol() {
    switch (this.config.protocol_type) {
      case 'ascii':
        return '\r\n';
      case 'binary':
        return Buffer.from([0x0D, 0x0A]); // CR LF
      case 'custom':
        return this.config.custom_delimiter || '\r\n';
      default:
        return '\r\n';
    }
  }

  // Parse weight data from scale
  parseWeightData(data) {
    try {
      const dataStr = data.toString().trim();
      logger.debug('Données reçues de la balance:', dataStr);

      // Enhanced patterns for RS232/RS485 balance protocols
      const patterns = [
        // RS232/RS485 Standard patterns
        // Pattern 1: "ST,GS,+017740 kg" (Status, Gross, Weight)
        /ST,GS,([+-]?\d+\.?\d*)\s*kg/i,
        // Pattern 2: "NET: 17740 kg" or "NET:17740kg"
        /NET:\s*([+-]?\d+\.?\d*)\s*kg/i,
        // Pattern 3: Simple weight "17740" or "+17740"
        /^([+-]?\d+\.?\d*)$/,
        // Pattern 4: "WEIGHT: 17740.5" or "W:17740.5"
        /(?:WEIGHT|W):\s*([+-]?\d+\.?\d*)/i,
        // Pattern 5: "+017740.0 kg STABLE" or "+017740.0kg"
        /([+-]?\d+\.?\d*)\s*kg(?:\s+(?:STABLE|UNSTABLE))?/i,
        // Pattern 6: RS485 format "G+017740kg" (Gross weight)
        /G([+-]?\d+\.?\d*)kg/i,
        // Pattern 7: RS485 format "N+017740kg" (Net weight)
        /N([+-]?\d+\.?\d*)kg/i,
        // Pattern 8: Digital indicator format "   17740.0 kg   "
        /^\s*([+-]?\d+\.?\d*)\s*kg\s*$/i,
        // Pattern 9: Modbus ASCII format ":010300000002F9\r\n"
        /:01030000([0-9A-F]{4})([0-9A-F]{2})\r?\n?/i,
        // Pattern 10: Binary format with header bytes
        /^\x02([+-]?\d+\.?\d*)\x03$/,
        // Pattern 11: Custom format for specific balance models
        /(?:WT|WEIGHT|POIDS)[\s:=]+([+-]?\d+\.?\d*)/i,
        // Pattern 12: Format with units "17740.5 KG" or "17740.5 T"
        /([+-]?\d+\.?\d*)\s*(?:KG|T|LB)/i
      ];

      for (const pattern of patterns) {
        const match = dataStr.match(pattern);
        if (match) {
          let weight = parseFloat(match[1]);

          // Handle Modbus format (convert hex to decimal)
          if (pattern.source.includes('01030000')) {
            const hexValue = match[1];
            weight = parseInt(hexValue, 16);
          }

          if (!isNaN(weight) && weight >= 0) {
            // Apply unit conversion if needed
            weight = this.applyUnitConversion(weight, dataStr);
            return weight;
          }
        }
      }

      // If no pattern matches, try to extract any number
      const numberMatch = dataStr.match(/([+-]?\d+\.?\d*)/);
      if (numberMatch) {
        const weight = parseFloat(numberMatch[1]);
        if (!isNaN(weight) && weight >= 0) {
          return this.applyUnitConversion(weight, dataStr);
        }
      }

      return null;
    } catch (error) {
      logger.error('Erreur lors du parsing du poids:', error);
      return null;
    }
  }

  // Apply unit conversion based on data format
  applyUnitConversion(weight, dataStr) {
    // Check for unit indicators in the data string
    const upperData = dataStr.toUpperCase();

    if (upperData.includes(' T') || upperData.includes('TON')) {
      // Convert tons to kg
      return weight * 1000;
    } else if (upperData.includes('LB') || upperData.includes('POUND')) {
      // Convert pounds to kg
      return weight * 0.453592;
    } else if (upperData.includes('G') && !upperData.includes('KG')) {
      // Convert grams to kg
      return weight / 1000;
    }

    // Default: assume kg
    return weight;
  }

  // Process weight reading
  processWeightReading(weight) {
    this.currentWeight = weight;

    // Check for stable reading using configured tolerance
    const tolerance = this.weightTolerance || 1.0;
    if (Math.abs(weight - this.lastStableWeight) < tolerance) {
      this.stableReadings++;
    } else {
      this.stableReadings = 0;
      this.lastStableWeight = weight;
    }

    // Emit weight update
    this.emit('weightUpdate', {
      weight: weight,
      stable: this.stableReadings >= this.requiredStableReadings,
      timestamp: new Date(),
      tolerance: tolerance,
      stableReadings: this.stableReadings,
      requiredStableReadings: this.requiredStableReadings
    });

    // If stable, emit stable weight
    if (this.stableReadings >= this.requiredStableReadings) {
      this.emit('stableWeight', {
        weight: weight,
        timestamp: new Date(),
        stableReadings: this.stableReadings
      });
    }
  }

  // Start reading interval for manual requests
  startReadingInterval() {
    if (this.readingInterval) {
      clearInterval(this.readingInterval);
    }

    const interval = this.autoReadInterval || 2000;
    this.readingInterval = setInterval(() => {
      if (this.isConnected && this.port && this.port.isOpen) {
        // Send read command (depends on scale protocol)
        this.requestWeight();
      }
    }, interval);

    logger.info(`Intervalle de lecture automatique démarré: ${interval}ms`);
  }

  // Request weight from scale
  requestWeight() {
    try {
      if (!this.port || !this.port.isOpen) {
        return;
      }

      // Use configured command format or default
      let command = this.config.command_format || 'R\\r\\n';

      // Convert escape sequences to actual characters
      command = command.replace(/\\r/g, '\r')
                      .replace(/\\n/g, '\n')
                      .replace(/\\t/g, '\t')
                      .replace(/\\x([0-9A-Fa-f]{2})/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)));

      // RS232/RS485 specific commands based on balance specifications
      const balanceCommands = {
        'standard': 'R\r\n',           // Standard read command
        'weight': 'W\r\n',             // Weight request
        'print': 'P\r\n',              // Print/display command
        'enquiry': '\x05',             // ENQ (Enquiry) ASCII control character
        'modbus_read': ':010300000002F9\r\n', // Modbus ASCII read holding registers
        'custom': command              // Use configured command
      };

      // Determine which command to use
      let commandToSend = balanceCommands.standard;

      if (this.config.protocol_type === 'modbus_ascii') {
        commandToSend = balanceCommands.modbus_read;
      } else if (this.config.command_format) {
        commandToSend = command;
      }

      logger.debug(`Envoi de la commande à la balance: ${JSON.stringify(commandToSend)}`);
      this.port.write(commandToSend);

    } catch (error) {
      logger.error('Erreur lors de la demande de poids:', error);
    }
  }

  // Get current weight
  getCurrentWeight() {
    if (this.manualMode) {
      return {
        weight: this.currentWeight,
        stable: true,
        manual: true,
        timestamp: new Date()
      };
    }

    return {
      weight: this.currentWeight,
      stable: this.stableReadings >= this.requiredStableReadings,
      manual: false,
      timestamp: new Date()
    };
  }

  // Set manual weight (for testing or manual input)
  setManualWeight(weight) {
    if (typeof weight !== 'number' || weight < 0) {
      throw new Error('Poids invalide');
    }

    this.currentWeight = weight;
    this.lastStableWeight = weight;
    this.stableReadings = this.requiredStableReadings;

    this.emit('weightUpdate', {
      weight: weight,
      stable: true,
      manual: true,
      timestamp: new Date()
    });

    logger.info('Poids manuel défini:', weight);
  }

  // Test connection
  async testConnection() {
    try {
      if (this.manualMode) {
        return {
          success: true,
          message: 'Mode manuel activé',
          manual: true
        };
      }

      if (!this.isConnected) {
        await this.connect();
      }

      // Send test command and wait for response
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          resolve({
            success: false,
            message: 'Timeout - Aucune réponse de la balance'
          });
        }, 5000);

        const onWeightUpdate = () => {
          clearTimeout(timeout);
          this.removeListener('weightUpdate', onWeightUpdate);
          resolve({
            success: true,
            message: 'Connexion réussie',
            currentWeight: this.currentWeight
          });
        };

        this.once('weightUpdate', onWeightUpdate);
        this.requestWeight();
      });

    } catch (error) {
      return {
        success: false,
        message: error.message
      };
    }
  }

  // Disconnect from weighbridge
  async disconnect() {
    try {
      if (this.readingInterval) {
        clearInterval(this.readingInterval);
        this.readingInterval = null;
      }

      if (this.port && this.port.isOpen) {
        await new Promise((resolve) => {
          this.port.close(resolve);
        });
      }

      this.isConnected = false;
      this.emit('disconnected');
      logger.info('Déconnexion de la balance');

    } catch (error) {
      logger.error('Erreur lors de la déconnexion:', error);
    }
  }

  // Update configuration
  async updateConfiguration(newConfig) {
    try {
      await update('weighbridge_config', newConfig, 'id = ?', [this.config.id]);
      await this.loadConfiguration();
      
      // Reconnect if needed
      if (this.isConnected && !this.config.manual_mode) {
        await this.disconnect();
        await this.connect();
      }

      logger.info('Configuration de balance mise à jour');
      return true;
    } catch (error) {
      logger.error('Erreur lors de la mise à jour de la configuration:', error);
      throw error;
    }
  }
}

// Singleton instance
const weighbridgeService = new WeighbridgeService();

module.exports = weighbridgeService;
