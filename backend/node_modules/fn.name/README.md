# fn.name

[![Version npm][version]](http://npm.im/fn.name)[![Build Status][build]](https://travis-ci.org/3rd-Eden/fn.name)[![Dependencies][david]](https://david-dm.org/3rd-Eden/fn.name)[![Coverage Status][cover]](https://coveralls.io/r/3rd-Eden/fn.name?branch=master)

[version]: http://img.shields.io/npm/v/fn.name.svg?style=flat-square
[build]: http://img.shields.io/travis/3rd-Eden/fn.name/master.svg?style=flat-square
[david]: https://img.shields.io/david/3rd-Eden/fn.name.svg?style=flat-square
[cover]: http://img.shields.io/coveralls/3rd-Eden/fn.name/master.svg?style=flat-square

Extract the name of a given function. Nothing more than that.

## Installation

This module is compatible with Browserify and Node.js and can be installed
using:

```
npm install --save fn.name
```

## Usage

Using this module is super simple, it exposes the function directly on the
exports so it can be required as followed:

```js
'use strict';

var name = require('fn.name');
```

Now that we have the `name` function we can pass it functions:

```js
console.log(name(function foo() {})) // foo
```

And that's it folks!

## License

MIT
