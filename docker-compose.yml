version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: port-tanit-db
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: port_tanit
      MYSQL_USER: portuser
      MYSQL_PASSWORD: portpass123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - port-tanit-network
    restart: unless-stopped

  backend:
    build: ./backend
    container_name: port-tanit-backend
    depends_on:
      - mysql
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: port_tanit
      DB_USER: portuser
      DB_PASSWORD: portpass123
      JWT_SECRET: port-tanit-secret-key-change-in-production
      PORT: 5555
      NODE_ENV: development
      # Serial port configuration for Windows
      SERIAL_PORT: ${SERIAL_PORT:-COM1}
      SERIAL_BAUD_RATE: ${SERIAL_BAUD_RATE:-9600}
      SERIAL_DATA_BITS: ${SERIAL_DATA_BITS:-8}
      SERIAL_STOP_BITS: ${SERIAL_STOP_BITS:-1}
      SERIAL_PARITY: ${SERIAL_PARITY:-none}
    ports:
      - "5555:5555"
    volumes:
      - ./backend:/app
      - /app/node_modules
      # Windows COM port mapping - Docker Desktop for Windows
      # Note: For Windows, we'll use host networking mode for serial access
    devices:
      # Linux/Unix serial port mapping (fallback)
      - /dev/ttyUSB0:/dev/ttyUSB0
      - /dev/ttyUSB1:/dev/ttyUSB1
      - /dev/ttyS0:/dev/ttyS0
      - /dev/ttyS1:/dev/ttyS1
    privileged: true  # Needed for serial port access
    # For Windows Docker Desktop, use host network mode for COM port access
    network_mode: ${NETWORK_MODE:-bridge}
    networks:
      - port-tanit-network
    restart: unless-stopped

  frontend:
    build: ./frontend
    container_name: port-tanit-frontend
    depends_on:
      - backend
    ports:
      - "3000:80"
    networks:
      - port-tanit-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  port-tanit-network:
    driver: bridge
