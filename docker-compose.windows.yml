# Docker Compose override for Windows COM port access
# Use this file for Windows environments with RS232 serial communication
# Run with: docker-compose -f docker-compose.yml -f docker-compose.windows.yml up

version: '3.8'

services:
  backend:
    # For Windows Docker Desktop, we need to use host networking
    # to access COM ports properly
    network_mode: host
    ports:
      - "5555:5555"
    environment:
      # Windows-specific serial port configuration
      SERIAL_PORT: ${SERIAL_PORT:-COM1}
      SERIAL_BAUD_RATE: ${SERIAL_BAUD_RATE:-9600}
      SERIAL_DATA_BITS: ${SERIAL_DATA_BITS:-8}
      SERIAL_STOP_BITS: ${SERIAL_STOP_BITS:-1}
      SERIAL_PARITY: ${SERIAL_PARITY:-none}
      PLATFORM: windows
    # Remove devices mapping as Windows uses COM ports differently
    devices: []
    volumes:
      - ./backend:/app
      - /app/node_modules
      # Windows-specific volume for COM port access
      - //./pipe/docker_engine://./pipe/docker_engine
    # Additional Windows-specific configuration
    extra_hosts:
      - "host.docker.internal:host-gateway"
