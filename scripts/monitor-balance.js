#!/usr/bin/env node

/**
 * Balance Data Monitor Script
 * 
 * This script connects to your KELI balance via COM port and displays
 * all raw data coming from the balance to help understand the data format.
 * 
 * Usage: node scripts/monitor-balance.js [COM_PORT] [BAUD_RATE]
 * Example: node scripts/monitor-balance.js COM3 9600
 */

const { SerialPort } = require('serialport');
const { ReadlineParser } = require('@serialport/parser-readline');

class BalanceMonitor {
  constructor(portPath = 'COM3', baudRate = 9600) {
    this.portPath = portPath;
    this.baudRate = baudRate;
    this.port = null;
    this.parser = null;
    this.dataCount = 0;
    this.startTime = new Date();
    this.lastWeight = null;
    this.commands = [
      'R\r\n',      // Read command
      'W\r\n',      // Weight command  
      'P\r\n',      // Print command
      '\x05',       // ENQ (Enquiry)
      'S\r\n',      // Status command
      'G\r\n',      // Gross weight
      'N\r\n',      // Net weight
      'T\r\n',      // Tare command
    ];
    this.currentCommandIndex = 0;
  }

  async connect() {
    console.log('🔌 KELI Balance Data Monitor');
    console.log('============================');
    console.log(`📍 Port: ${this.portPath}`);
    console.log(`⚡ Baud Rate: ${this.baudRate}`);
    console.log(`🕐 Started: ${this.startTime.toLocaleString()}`);
    console.log('============================\n');

    try {
      this.port = new SerialPort({
        path: this.portPath,
        baudRate: this.baudRate,
        dataBits: 8,
        stopBits: 1,
        parity: 'none',
        autoOpen: false
      });

      // Setup event handlers
      this.port.on('error', (error) => {
        console.error('❌ Port Error:', error.message);
        process.exit(1);
      });

      this.port.on('close', () => {
        console.log('\n📴 Port closed. Exiting...');
        process.exit(0);
      });

      this.port.on('open', () => {
        console.log('✅ Connected to balance successfully!\n');
        this.setupParser();
        this.startMonitoring();
      });

      // Open the port
      await new Promise((resolve, reject) => {
        this.port.open((error) => {
          if (error) reject(error);
          else resolve();
        });
      });

    } catch (error) {
      console.error('❌ Connection failed:', error.message);
      process.exit(1);
    }
  }

  setupParser() {
    // Try different delimiters to catch all possible formats
    this.parser = this.port.pipe(new ReadlineParser({ 
      delimiter: /\r?\n/,
      includeDelimiter: false 
    }));

    this.parser.on('data', (data) => {
      this.handleData(data);
    });

    // Also listen for raw data to catch non-line-terminated data
    this.port.on('data', (rawData) => {
      this.handleRawData(rawData);
    });
  }

  handleData(data) {
    this.dataCount++;
    const timestamp = new Date().toLocaleTimeString();
    const dataStr = data.toString().trim();
    
    if (dataStr.length > 0) {
      console.log(`📨 [${timestamp}] #${this.dataCount} LINE DATA:`);
      console.log(`   Raw: "${dataStr}"`);
      console.log(`   Hex: ${Buffer.from(dataStr).toString('hex')}`);
      console.log(`   Length: ${dataStr.length} chars`);
      
      // Try to parse weight
      const weight = this.parseWeight(dataStr);
      if (weight !== null) {
        console.log(`   ⚖️  Parsed Weight: ${weight} kg`);
        if (this.lastWeight !== weight) {
          console.log(`   📈 Weight Changed: ${this.lastWeight} → ${weight} kg`);
          this.lastWeight = weight;
        }
      }
      console.log('');
    }
  }

  handleRawData(rawData) {
    // Only log raw data if it doesn't end with line terminators
    const dataStr = rawData.toString();
    if (!dataStr.endsWith('\r') && !dataStr.endsWith('\n') && !dataStr.endsWith('\r\n')) {
      const timestamp = new Date().toLocaleTimeString();
      console.log(`📡 [${timestamp}] RAW DATA (no line ending):`);
      console.log(`   Raw: "${dataStr}"`);
      console.log(`   Hex: ${rawData.toString('hex')}`);
      console.log('');
    }
  }

  parseWeight(dataStr) {
    // KELI balance parsing patterns based on common formats
    const patterns = [
      // Standard KELI formats
      /([+-]?\d+\.?\d*)\s*kg/i,                    // "123.45 kg"
      /([+-]?\d+\.?\d*)\s*KG/i,                    // "123.45 KG"
      /GS,([+-]?\d+\.?\d*)/i,                      // "GS,123.45"
      /ST,GS,([+-]?\d+\.?\d*)/i,                   // "ST,GS,123.45"
      /NET:\s*([+-]?\d+\.?\d*)/i,                  // "NET: 123.45"
      /GROSS:\s*([+-]?\d+\.?\d*)/i,                // "GROSS: 123.45"
      /W:\s*([+-]?\d+\.?\d*)/i,                    // "W: 123.45"
      /([+-]?\d+\.?\d*)\s*T/i,                     // "1.234 T" (tons)
      /^([+-]?\d+\.?\d*)$/,                        // Simple number "123.45"
      // KELI specific patterns
      /ECC\s+([+-]?\d+\.?\d*)/i,                   // "ECC 123.45" (from display)
      /([+-]?\d{1,6}\.?\d{0,3})\s*$/,              // Weight only format
    ];

    for (const pattern of patterns) {
      const match = dataStr.match(pattern);
      if (match) {
        let weight = parseFloat(match[1]);
        if (!isNaN(weight)) {
          // Convert tons to kg if needed
          if (dataStr.toUpperCase().includes(' T') && !dataStr.toUpperCase().includes('KG')) {
            weight = weight * 1000;
          }
          return weight;
        }
      }
    }
    return null;
  }

  startMonitoring() {
    console.log('🎯 Starting continuous monitoring...');
    console.log('💡 The balance may send data automatically, or we may need to request it.');
    console.log('🔄 Trying different commands to request weight data...\n');

    // Try sending commands periodically to request data
    this.commandInterval = setInterval(() => {
      this.sendCommand();
    }, 3000); // Every 3 seconds

    // Also try a simple read command immediately
    setTimeout(() => {
      this.sendCommand();
    }, 1000);
  }

  sendCommand() {
    if (!this.port || !this.port.isOpen) return;

    const command = this.commands[this.currentCommandIndex];
    const commandName = this.getCommandName(command);
    
    console.log(`📤 Sending command: ${commandName} (${JSON.stringify(command)})`);
    
    try {
      this.port.write(command);
      this.currentCommandIndex = (this.currentCommandIndex + 1) % this.commands.length;
    } catch (error) {
      console.error('❌ Error sending command:', error.message);
    }
  }

  getCommandName(command) {
    const commandMap = {
      'R\r\n': 'READ',
      'W\r\n': 'WEIGHT',
      'P\r\n': 'PRINT',
      '\x05': 'ENQ',
      'S\r\n': 'STATUS',
      'G\r\n': 'GROSS',
      'N\r\n': 'NET',
      'T\r\n': 'TARE'
    };
    return commandMap[command] || 'UNKNOWN';
  }

  showStats() {
    const runtime = Math.floor((new Date() - this.startTime) / 1000);
    console.log('\n📊 MONITORING STATISTICS:');
    console.log(`   Runtime: ${runtime} seconds`);
    console.log(`   Data packets received: ${this.dataCount}`);
    console.log(`   Last weight: ${this.lastWeight || 'None'} kg`);
    console.log(`   Commands tried: ${this.commands.length} different types`);
  }

  stop() {
    console.log('\n🛑 Stopping monitor...');
    if (this.commandInterval) {
      clearInterval(this.commandInterval);
    }
    if (this.port && this.port.isOpen) {
      this.port.close();
    }
    this.showStats();
  }
}

// Handle script arguments
const args = process.argv.slice(2);
const portPath = args[0] || 'COM3';
const baudRate = parseInt(args[1]) || 9600;

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⚠️  Received CTRL+C, shutting down gracefully...');
  if (monitor) {
    monitor.stop();
  }
  process.exit(0);
});

// Start monitoring
const monitor = new BalanceMonitor(portPath, baudRate);

console.log('🚀 Starting KELI Balance Monitor...');
console.log('📝 This will show all data coming from your balance.');
console.log('🔍 We\'ll try different commands to see what works.');
console.log('⏹️  Press CTRL+C to stop monitoring.\n');

monitor.connect().catch(console.error);
