#!/usr/bin/env node

/**
 * KELI Balance Comprehensive Diagnostics
 * Tests different baud rates, commands, and configurations
 * 
 * Usage: node balance-diagnostics.js [PORT] [INITIAL_BAUD]
 * Example: node balance-diagnostics.js COM4 9600
 */

const { SerialPort } = require('serialport');

class BalanceDiagnostics {
  constructor(portPath = 'COM4', initialBaud = 9600) {
    this.portPath = portPath;
    this.initialBaud = initialBaud;
    this.currentPort = null;
    this.testResults = [];
    
    // Common baud rates for KELI balances
    this.baudRates = [600, 1200, 2400, 4800, 9600, 19200];
    
    // Extended command set for KELI balances
    this.commands = [
      // Standard commands
      { name: 'READ', cmd: 'R\r\n', desc: 'Read weight' },
      { name: 'WEIGHT', cmd: 'W\r\n', desc: 'Weight command' },
      { name: 'PRINT', cmd: 'P\r\n', desc: 'Print weight' },
      { name: 'STATUS', cmd: 'S\r\n', desc: 'Status request' },
      { name: 'GROSS', cmd: 'G\r\n', desc: 'Gross weight' },
      { name: 'NET', cmd: 'N\r\n', desc: 'Net weight' },
      { name: 'TARE', cmd: 'T\r\n', desc: 'Tare command' },
      
      // Control characters
      { name: 'ENQ', cmd: '\x05', desc: 'Enquiry (ASCII 5)' },
      { name: 'ACK', cmd: '\x06', desc: 'Acknowledge (ASCII 6)' },
      { name: 'DC1', cmd: '\x11', desc: 'Device Control 1 (ASCII 17)' },
      { name: 'DC2', cmd: '\x12', desc: 'Device Control 2 (ASCII 18)' },
      { name: 'DC3', cmd: '\x13', desc: 'Device Control 3 (ASCII 19)' },
      { name: 'DC4', cmd: '\x14', desc: 'Device Control 4 (ASCII 20)' },
      
      // KELI specific commands (based on manual research)
      { name: 'KELI_READ', cmd: 'RD\r\n', desc: 'KELI Read' },
      { name: 'KELI_WEIGHT', cmd: 'WT\r\n', desc: 'KELI Weight' },
      { name: 'KELI_STATUS', cmd: 'ST\r\n', desc: 'KELI Status' },
      { name: 'KELI_ZERO', cmd: 'ZE\r\n', desc: 'KELI Zero' },
      { name: 'KELI_TARE', cmd: 'TA\r\n', desc: 'KELI Tare' },
      
      // Alternative formats
      { name: 'READ_CR', cmd: 'R\r', desc: 'Read with CR only' },
      { name: 'READ_LF', cmd: 'R\n', desc: 'Read with LF only' },
      { name: 'READ_SPACE', cmd: 'R ', desc: 'Read with space' },
      { name: 'QUESTION', cmd: '?\r\n', desc: 'Question mark query' },
      { name: 'ASTERISK', cmd: '*\r\n', desc: 'Asterisk command' },
      
      // Numeric commands
      { name: 'CMD_1', cmd: '1\r\n', desc: 'Command 1' },
      { name: 'CMD_2', cmd: '2\r\n', desc: 'Command 2' },
      { name: 'CMD_3', cmd: '3\r\n', desc: 'Command 3' },
    ];
  }

  async start() {
    console.log('🔬 KELI Balance Comprehensive Diagnostics');
    console.log('==========================================');
    console.log(`📍 Port: ${this.portPath}`);
    console.log(`🕐 Started: ${new Date().toLocaleString()}`);
    console.log('==========================================\n');

    // Test different baud rates
    for (const baudRate of this.baudRates) {
      console.log(`\n🔍 Testing Baud Rate: ${baudRate}`);
      console.log('─'.repeat(40));
      
      const success = await this.testBaudRate(baudRate);
      if (success) {
        console.log(`✅ Found working configuration at ${baudRate} baud!`);
        break;
      }
    }

    this.printSummary();
  }

  async testBaudRate(baudRate) {
    try {
      // Close existing port if open
      if (this.currentPort && this.currentPort.isOpen) {
        await this.closePort();
      }

      // Open port with new baud rate
      this.currentPort = new SerialPort({
        path: this.portPath,
        baudRate: baudRate,
        dataBits: 8,
        stopBits: 1,
        parity: 'none',
        rtscts: false,
        xon: false,
        xoff: false,
        autoOpen: false
      });

      await this.openPort();
      console.log(`✅ Connected at ${baudRate} baud`);

      // Test commands at this baud rate
      const responses = await this.testCommands(baudRate);
      
      // If we got any responses, this baud rate works
      return responses > 0;

    } catch (error) {
      console.log(`❌ Failed at ${baudRate} baud: ${error.message}`);
      return false;
    }
  }

  async testCommands(baudRate) {
    let responseCount = 0;
    
    for (const command of this.commands) {
      try {
        console.log(`📤 Testing: ${command.name} (${command.desc})`);
        
        const response = await this.sendCommandAndWait(command.cmd, 1000);
        
        if (response && response.length > 0) {
          responseCount++;
          console.log(`   ✅ Response: "${response}"`);
          console.log(`   📊 Hex: ${Buffer.from(response).toString('hex')}`);
          
          this.testResults.push({
            baudRate,
            command: command.name,
            response,
            success: true
          });
        } else {
          console.log(`   ⚪ No response`);
        }
        
        // Small delay between commands
        await this.delay(200);
        
      } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
      }
    }
    
    return responseCount;
  }

  async sendCommandAndWait(command, timeout = 1000) {
    return new Promise((resolve) => {
      let responseData = '';
      let timeoutId;
      
      const dataHandler = (data) => {
        responseData += data.toString();
      };
      
      this.currentPort.on('data', dataHandler);
      
      // Set timeout
      timeoutId = setTimeout(() => {
        this.currentPort.removeListener('data', dataHandler);
        resolve(responseData.trim());
      }, timeout);
      
      // Send command
      this.currentPort.write(command, (err) => {
        if (err) {
          clearTimeout(timeoutId);
          this.currentPort.removeListener('data', dataHandler);
          resolve('');
        }
      });
    });
  }

  async openPort() {
    return new Promise((resolve, reject) => {
      this.currentPort.open((err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async closePort() {
    return new Promise((resolve) => {
      if (this.currentPort && this.currentPort.isOpen) {
        this.currentPort.close(() => resolve());
      } else {
        resolve();
      }
    });
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  printSummary() {
    console.log('\n📊 DIAGNOSTIC SUMMARY');
    console.log('=====================');
    
    if (this.testResults.length === 0) {
      console.log('❌ No responses received from balance');
      console.log('\n💡 TROUBLESHOOTING SUGGESTIONS:');
      console.log('1. Check physical connections (USB cable, DB9 connector)');
      console.log('2. Verify balance is powered on and ready');
      console.log('3. Try different COM port (check Device Manager)');
      console.log('4. Check if balance needs specific setup/mode');
      console.log('5. Verify cable is not null-modem (should be straight-through)');
      console.log('6. Try hardware handshaking (RTS/CTS)');
    } else {
      console.log(`✅ Found ${this.testResults.length} working command(s):`);
      
      this.testResults.forEach(result => {
        console.log(`   📍 ${result.baudRate} baud - ${result.command}: "${result.response}"`);
      });
      
      console.log('\n🎯 RECOMMENDED CONFIGURATION:');
      const bestResult = this.testResults[0];
      console.log(`   Baud Rate: ${bestResult.baudRate}`);
      console.log(`   Command: ${bestResult.command}`);
      console.log(`   Response Format: "${bestResult.response}"`);
    }
  }
}

// Main execution
async function main() {
  const portPath = process.argv[2] || 'COM4';
  const initialBaud = parseInt(process.argv[3]) || 9600;
  
  const diagnostics = new BalanceDiagnostics(portPath, initialBaud);
  
  try {
    await diagnostics.start();
  } catch (error) {
    console.error('❌ Diagnostic failed:', error.message);
  } finally {
    if (diagnostics.currentPort && diagnostics.currentPort.isOpen) {
      await diagnostics.closePort();
    }
    process.exit(0);
  }
}

// Handle CTRL+C gracefully
process.on('SIGINT', async () => {
  console.log('\n⚠️  Received CTRL+C, shutting down...');
  process.exit(0);
});

if (require.main === module) {
  main();
}
