#!/usr/bin/env node

/**
 * COM Port Detection Script for Windows USB-to-RS232 Adapters
 * 
 * This script helps identify available COM ports on Windows,
 * specifically for USB-to-RS232 adapters used with weighbridge scales.
 * 
 * Usage: node scripts/detect-com-ports.js
 */

const { SerialPort } = require('serialport');

async function detectComPorts() {
  console.log('🔍 Detecting COM ports for USB-to-RS232 adapters...\n');

  try {
    const ports = await SerialPort.list();
    
    if (ports.length === 0) {
      console.log('❌ No serial ports found.');
      console.log('   Make sure your USB-to-RS232 adapter is connected and drivers are installed.');
      return;
    }

    console.log(`✅ Found ${ports.length} serial port(s):\n`);

    // Filter and display COM ports (Windows)
    const comPorts = ports.filter(port => port.path.startsWith('COM'));
    
    if (comPorts.length === 0) {
      console.log('❌ No COM ports found. This script is designed for Windows systems.');
      console.log('   All available ports:');
      ports.forEach((port, index) => {
        console.log(`   ${index + 1}. ${port.path} - ${port.manufacturer || 'Unknown'}`);
      });
      return;
    }

    // Display COM ports with details
    comPorts.forEach((port, index) => {
      const isUSBAdapter = port.manufacturer && (
        port.manufacturer.toLowerCase().includes('ftdi') ||
        port.manufacturer.toLowerCase().includes('prolific') ||
        port.manufacturer.toLowerCase().includes('silicon') ||
        port.manufacturer.toLowerCase().includes('ch340') ||
        port.manufacturer.toLowerCase().includes('cp210')
      );

      console.log(`📍 COM Port ${index + 1}:`);
      console.log(`   Path: ${port.path}`);
      console.log(`   Manufacturer: ${port.manufacturer || 'Unknown'}`);
      console.log(`   Serial Number: ${port.serialNumber || 'Unknown'}`);
      console.log(`   PnP ID: ${port.pnpId || 'Unknown'}`);
      console.log(`   USB Adapter: ${isUSBAdapter ? '✅ Yes' : '❓ Unknown'}`);
      
      if (isUSBAdapter) {
        console.log(`   🎯 RECOMMENDED for weighbridge connection`);
      }
      console.log('');
    });

    // Provide configuration guidance
    console.log('📋 Configuration Guidance:');
    console.log('');
    
    const recommendedPort = comPorts.find(port => 
      port.manufacturer && (
        port.manufacturer.toLowerCase().includes('ftdi') ||
        port.manufacturer.toLowerCase().includes('prolific') ||
        port.manufacturer.toLowerCase().includes('silicon')
      )
    ) || comPorts[0];

    console.log(`🔧 Recommended .env configuration:`);
    console.log(`   SERIAL_PORT=${recommendedPort.path}`);
    console.log(`   SERIAL_BAUD_RATE=9600`);
    console.log(`   SERIAL_DATA_BITS=8`);
    console.log(`   SERIAL_STOP_BITS=1`);
    console.log(`   SERIAL_PARITY=none`);
    console.log('');

    console.log('🧪 Test your connection:');
    console.log(`   1. Update your .env file with SERIAL_PORT=${recommendedPort.path}`);
    console.log('   2. Start the application: docker-compose up -d');
    console.log('   3. Test the port via API or web interface');
    console.log('');

    console.log('💡 Troubleshooting tips:');
    console.log('   - If no USB adapters are detected, check Device Manager');
    console.log('   - Ensure drivers are installed for your adapter');
    console.log('   - Try different USB ports if connection is unstable');
    console.log('   - Test with PuTTY first to verify basic communication');

  } catch (error) {
    console.error('❌ Error detecting COM ports:', error.message);
    console.log('');
    console.log('🔧 Possible solutions:');
    console.log('   - Make sure Node.js serialport package is installed');
    console.log('   - Run: npm install serialport');
    console.log('   - Check if you have permission to access serial ports');
  }
}

// Test a specific COM port
async function testComPort(portPath, baudRate = 9600) {
  console.log(`🧪 Testing ${portPath} at ${baudRate} baud...`);

  return new Promise((resolve) => {
    const testPort = new SerialPort({
      path: portPath,
      baudRate: baudRate,
      dataBits: 8,
      stopBits: 1,
      parity: 'none',
      autoOpen: false
    });

    const timeout = setTimeout(() => {
      testPort.close();
      console.log(`❌ ${portPath}: Connection timeout`);
      resolve(false);
    }, 5000);

    testPort.on('error', (error) => {
      clearTimeout(timeout);
      console.log(`❌ ${portPath}: ${error.message}`);
      resolve(false);
    });

    testPort.on('open', () => {
      clearTimeout(timeout);
      console.log(`✅ ${portPath}: Connection successful`);
      testPort.close();
      resolve(true);
    });

    testPort.open();
  });
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length > 0 && args[0].startsWith('COM')) {
    // Test specific port
    const portPath = args[0];
    const baudRate = args[1] ? parseInt(args[1]) : 9600;
    await testComPort(portPath, baudRate);
  } else {
    // Detect all ports
    await detectComPorts();
  }
}

// Handle script execution
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { detectComPorts, testComPort };
