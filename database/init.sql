-- Port Tanit Weighbridge Management System
-- Database Initialization Script

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS port_tanit CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE port_tanit;

-- Table: users
DROP TABLE IF EXISTS users;
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  full_name VARCHAR(100),
  role ENUM('admin', 'operator', 'viewer') DEFAULT 'operator',
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login TIMESTAMP NULL,
  INDEX idx_username (username),
  INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: products
DROP TABLE IF EXISTS products;
CREATE TABLE products (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(20) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  price_per_kg DECIMAL(10, 2) NOT NULL,
  description TEXT,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_code (code),
  INDEX idx_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: weighings
DROP TABLE IF EXISTS weighings;
CREATE TABLE weighings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  ticket_number VARCHAR(50) UNIQUE NOT NULL,
  matricule VARCHAR(50) NOT NULL,
  product_id INT NOT NULL,
  client_name VARCHAR(200),
  origin VARCHAR(100),
  destination VARCHAR(100),
  entry_weight DECIMAL(10, 2),
  entry_datetime TIMESTAMP NULL,
  entry_operator_id INT,
  exit_weight DECIMAL(10, 2),
  exit_datetime TIMESTAMP NULL,
  exit_operator_id INT,
  net_weight DECIMAL(10, 2),
  amount_to_pay DECIMAL(12, 2),
  payment_status ENUM('pending', 'paid') DEFAULT 'pending',
  status ENUM('entry', 'completed', 'cancelled') DEFAULT 'entry',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES products(id),
  FOREIGN KEY (entry_operator_id) REFERENCES users(id),
  FOREIGN KEY (exit_operator_id) REFERENCES users(id),
  INDEX idx_ticket_number (ticket_number),
  INDEX idx_matricule (matricule),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at),
  INDEX idx_entry_datetime (entry_datetime),
  INDEX idx_exit_datetime (exit_datetime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: settings
DROP TABLE IF EXISTS settings;
CREATE TABLE settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  setting_key VARCHAR(100) UNIQUE NOT NULL,
  setting_value TEXT,
  description VARCHAR(255),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: weighbridge_config
DROP TABLE IF EXISTS weighbridge_config;
CREATE TABLE weighbridge_config (
  id INT PRIMARY KEY AUTO_INCREMENT,
  connection_type ENUM('serial', 'tcp', 'udp', 'modbus', 'rs232', 'rs485') DEFAULT 'serial',
  port_address VARCHAR(100),
  baud_rate INT DEFAULT 9600,
  data_bits INT DEFAULT 8,
  stop_bits INT DEFAULT 1,
  parity VARCHAR(10) DEFAULT 'none',
  flow_control ENUM('none', 'xon_xoff', 'rts_cts', 'dtr_dsr') DEFAULT 'none',
  protocol_type ENUM('ascii', 'binary', 'custom', 'modbus_rtu', 'modbus_ascii') DEFAULT 'ascii',
  protocol_description TEXT,
  command_format VARCHAR(50) DEFAULT 'R\\r\\n',
  response_format VARCHAR(100) DEFAULT 'weight_kg',
  timeout_ms INT DEFAULT 5000,
  retry_attempts INT DEFAULT 3,
  stable_readings_required INT DEFAULT 3,
  weight_tolerance DECIMAL(5,2) DEFAULT 1.00,
  manual_mode BOOLEAN DEFAULT FALSE,
  auto_read_interval INT DEFAULT 2000,
  platform VARCHAR(20) DEFAULT 'linux',
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_active (active),
  INDEX idx_connection_type (connection_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default admin user (password: admin123)
INSERT INTO users (username, password_hash, full_name, role) VALUES
('admin', '$2a$10$7A6psS4v7yBkxhLbzA7hGeno918Adv4o62lkTWCCPcl0jEEMp520C', 'Administrateur', 'admin');

-- Insert default products
INSERT INTO products (code, name, price_per_kg, description) VALUES
('RIO1', 'Arena de rio', 1200.00, 'Produit de glace de Rio'),
('FISH', 'Poisson', 1200.00, 'Produit de poisson');

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, description) VALUES
('port_name', 'Port de Tanit - Mauritanie', 'Nom du port'),
('port_address', 'ROUTE NOUADHIBOU PK60', 'Adresse du port'),
('port_city', 'NOUAKCHOTT - MAURITANIE', 'Ville du port'),
('port_phone', '+222 22 72 22 72', 'Téléphone du port'),
('port_email', '<EMAIL>', 'Email du port'),
('currency', 'Ouguiya', 'Devise utilisée'),
('default_price_per_kg', '1200', 'Prix par défaut par kg'),
('ticket_prefix', 'PT', 'Préfixe des tickets'),
('auto_backup_enabled', 'true', 'Sauvegarde automatique activée'),
('backup_retention_days', '30', 'Jours de rétention des sauvegardes');

-- Insert default weighbridge configuration
INSERT INTO weighbridge_config (
  connection_type, port_address, baud_rate, data_bits, stop_bits, parity,
  flow_control, protocol_type, command_format, response_format,
  timeout_ms, retry_attempts, stable_readings_required, weight_tolerance,
  manual_mode, auto_read_interval, platform, active
) VALUES
('rs232', 'COM1', 9600, 8, 1, 'none', 'none', 'ascii', 'R\\r\\n', 'weight_kg', 5000, 3, 3, 1.00, true, 2000, 'windows', true);

SET FOREIGN_KEY_CHECKS = 1;
